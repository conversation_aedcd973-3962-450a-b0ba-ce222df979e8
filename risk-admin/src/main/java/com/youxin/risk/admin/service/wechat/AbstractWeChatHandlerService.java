package com.youxin.risk.admin.service.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.tools.wechat.QwClient;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Stopwatch;

/**
 * @desc 企业微信消息处理抽象服务
 */
public abstract class AbstractWeChatHandlerService<T> {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private QwClient qwClient;

    @Value("${qw.agent.id:1000146}")
    private Integer agentId;

    private static final String SEND_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send";

    /**
     * 获取目标用户ID（企业微信应用消息）
     * @param params 参数
     * @return 用户ID，必须返回有效的用户ID
     */
    protected abstract String getTargetUserId(Map<String, String> params);

    /**
     * 处理消息
     * @param params 参数
     */
    public void handler(Map<String, String> params) {
        LoggerProxy.info("企业微信消息处理", logger, "开始，消息类型={},params={}", supportMsgType(), params);
        Stopwatch stopwatch = Stopwatch.createStarted();
        T t = processMessage(params);
        LoggerProxy.info("企业微信消息处理", logger, "处理消息结果={}，处理耗时={}ms", JSON.toJSONString(t),
                stopwatch.elapsed(TimeUnit.MILLISECONDS));

        String responseMessage = buildResponseMessage(t, params);
        LoggerProxy.info("企业微信消息处理", logger, "构建好的响应消息={}", responseMessage);

        sendMessage(responseMessage, params);
        LoggerProxy.info("企业微信消息处理", logger, "发送响应消息结束");
    }

    /**
     * 处理消息
     * @param params 参数
     * @return 处理结果
     */
    protected abstract T processMessage(Map<String, String> params);

    /**
     * 构建响应消息
     * @param t 处理结果
     * @param params 参数
     * @return 响应消息
     */
    protected abstract String buildResponseMessage(T t, Map<String, String> params);

    /**
     * 支持的消息类型
     * @return 消息类型
     */
    protected abstract String supportMsgType();

    /**
     * 发送消息（兼容旧版本）
     * @param msg 消息内容
     */
    protected void sendMessage(String msg) {
        sendMessage(msg, null);
    }

    /**
     * 发送消息
     * @param msg 消息内容
     * @param params 参数
     */
    protected void sendMessage(String msg, Map<String, String> params) {
        if (msg == null) {
            LoggerProxy.warn("发送企微消息", logger, "发送消息体为空，不执行发送操作");
            return;
        }

        if (msg.length() > 4000) {
            LoggerProxy.warn("发送企微消息", logger, "消息长度超过4000字节，执行截断操作，截断前消息为={}", msg);
            msg = msg.substring(0, 4000);
        }

        // 获取目标用户ID并发送到企业微信应用
        String targetUserId = params != null ? getTargetUserId(params) : null;

        if (targetUserId != null && !targetUserId.trim().isEmpty()) {
            sendToWeChatApp(msg, targetUserId);
        } else {
            LoggerProxy.error("sendMessage", logger, "无法获取有效的用户ID，消息发送失败");
            throw new RuntimeException("无法获取有效的用户ID，消息发送失败");
        }
    }

    /**
     * 发送消息到企业微信应用
     * @param msg 消息内容
     * @param userId 用户ID
     */
    private void sendToWeChatApp(String msg, String userId) {
        try {
            String accessToken = qwClient.getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendToWeChatApp", logger, "获取企业微信access_token失败");
                throw new RuntimeException("获取企业微信access_token失败");
            }

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", msg);
            message.put("markdown", markdown);

            // 发送消息
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), 10000);

            LoggerProxy.info("sendToWeChatApp", logger,
                    "发送企业微信应用消息完成, userId={}, response={}", userId, response);

            // 检查响应
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson.getIntValue("errcode") != 0) {
                LoggerProxy.error("sendToWeChatApp", logger,
                        "企业微信应用消息发送失败, errcode={}, errmsg={}",
                        responseJson.getIntValue("errcode"), responseJson.getString("errmsg"));
                throw new RuntimeException("企业微信应用消息发送失败: " + responseJson.getString("errmsg"));
            }

        } catch (Exception e) {
            LoggerProxy.error("sendToWeChatApp", logger, "发送企业微信应用消息异常", e);
            throw new RuntimeException("发送企业微信应用消息异常: " + e.getMessage(), e);
        }
    }



    /**
     * 格式化警告消息
     * @param message 消息
     * @return 格式化后的消息
     */
    protected String warnMessage(String message) {
        return String.format("<font color=\"warning\">%s</font>", message);
    }

    /**
     * 格式化信息消息
     * @param message 消息
     * @return 格式化后的消息
     */
    protected String infoMessage(String message) {
        return String.format("<font color=\"info\">%s</font>", message);
    }
}